import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider
import argparse
import logging
import glob
from dataprocess.volume import Volume
import hydra
from omegaconf import DictConfig

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def find_sdf_files(base_dir, organelle_type="mito"):
    """Find all SDF files for the specified organelle type

    Args:
        base_dir: Dataset root directory
        organelle_type: Organelle type (default: "mito")

    Returns:
        sdf_files: List of SDF file paths
    """
    sdf_files = []

    # Traverse train and val directories
    for split in ["train", "val"]:
        split_path = os.path.join(base_dir, split)
        if not os.path.exists(split_path):
            continue

        # Traverse dataset ID directories
        for dataset_id in os.listdir(split_path):
            dataset_path = os.path.join(split_path, dataset_id)
            if not os.path.isdir(dataset_path):
                continue

            # Find SDF directory for the specified organelle
            sdf_path = os.path.join(dataset_path, "sdf", organelle_type)
            if not os.path.exists(sdf_path):
                continue

            # Find all zst files
            for sdf_file in glob.glob(os.path.join(sdf_path, "*.zst")):
                sdf_files.append(sdf_file)

    logger.info(f"Found {len(sdf_files)} {organelle_type} SDF files")
    return sdf_files


def visualize_sdf(sdf_file, show_binary=False, show_3d=False):
    """Visualize SDF file

    Args:
        sdf_file: SDF file path
        show_binary: Whether to show binary mask
        show_3d: Whether to show 3D view
    """
    # Load SDF
    volume = Volume(sdf_file)
    volume.load()
    sdf_data = volume.volume

    # Get filename and path information
    filename = os.path.basename(sdf_file)
    dataset_path = os.path.dirname(os.path.dirname(os.path.dirname(sdf_file)))
    dataset_name = os.path.basename(dataset_path)

    # Create binary mask (SDF > 0 region)
    binary_mask = sdf_data > 0

    # Create interactive visualization
    fig = plt.figure(figsize=(15, 8))
    fig.suptitle(f"SDF Visualization: {dataset_name}/{filename}", fontsize=16)

    # Initial slice index
    z_mid = sdf_data.shape[0] // 2

    # Create subplots
    if show_binary:
        ax1 = fig.add_subplot(121)
        ax2 = fig.add_subplot(122)

        # Show SDF slice
        im1 = ax1.imshow(sdf_data[z_mid], cmap="seismic", vmin=-30, vmax=30)
        ax1.set_title(f"SDF Slice (z={z_mid})")
        plt.colorbar(im1, ax=ax1, label="Distance Value")

        # Show binary mask slice
        im2 = ax2.imshow(binary_mask[z_mid], cmap="gray")
        ax2.set_title(f"Binary Mask Slice (z={z_mid})")
    else:
        ax = fig.add_subplot(111)

        # Show SDF slice
        im = ax.imshow(sdf_data[z_mid], cmap="seismic", vmin=-30, vmax=30)
        ax.set_title(f"SDF Slice (z={z_mid})")
        plt.colorbar(im, ax=ax, label="Distance Value")

    # Add slider to control slice
    # Adjust bottom margin to make room for slider
    plt.subplots_adjust(bottom=0.25)  # Increase bottom margin
    ax_slider = plt.axes([0.15, 0.1, 0.7, 0.03])  # Move slider down
    slider = Slider(
        ax=ax_slider,
        label="Z Slice",
        valmin=0,
        valmax=sdf_data.shape[0] - 1,
        valinit=z_mid,
        valstep=1,
    )

    # Update function
    def update(_):  # Parameter not used but required by Slider
        z = int(slider.val)
        if show_binary:
            im1.set_data(sdf_data[z])
            ax1.set_title(f"SDF Slice (z={z})")
            im2.set_data(binary_mask[z])
            ax2.set_title(f"Binary Mask Slice (z={z})")
        else:
            im.set_data(sdf_data[z])
            ax.set_title(f"SDF Slice (z={z})")
        fig.canvas.draw_idle()

    slider.on_changed(update)

    # Show 3D view (if needed)
    if show_3d:
        try:
            from mpl_toolkits.mplot3d import (
                Axes3D,
            )  # noqa: F401, required for 3D projection
            from skimage import measure

            # Extract isosurface
            verts, faces, _, _ = measure.marching_cubes(binary_mask, 0.5)

            # Create new window for 3D view
            fig_3d = plt.figure(figsize=(10, 10))
            ax_3d = fig_3d.add_subplot(111, projection="3d")
            ax_3d.plot_trisurf(
                verts[:, 0],
                verts[:, 1],
                verts[:, 2],
                triangles=faces,
                cmap="viridis",
                alpha=0.7,
            )
            ax_3d.set_title(f"3D View: {dataset_name}/{filename}")

            # Set viewpoint
            ax_3d.view_init(elev=30, azim=45)
            ax_3d.set_xlabel("X")
            ax_3d.set_ylabel("Y")
            ax_3d.set_zlabel("Z")
        except ImportError:
            logger.warning("Could not import libraries required for 3D visualization")
        except Exception as e:
            logger.error(f"3D visualization failed: {str(e)}")

    # Don't use tight_layout() as it would override our slider position
    plt.show()


def visualize_all_sdfs(
    sdf_files, show_binary=False
):  # show_binary is kept for API consistency
    """Visualize middle slices of all SDF files

    Args:
        sdf_files: List of SDF file paths
        show_binary: Whether to show binary mask (not used in overview mode)
    """
    if not sdf_files:
        logger.warning("No SDF files found")
        return

    # Determine grid size
    n_files = len(sdf_files)
    n_cols = min(3, n_files)
    n_rows = (n_files + n_cols - 1) // n_cols

    # Create figure
    _, axes = plt.subplots(
        n_rows, n_cols, figsize=(15, 5 * n_rows)
    )  # Figure object not used
    if n_rows == 1 and n_cols == 1:
        axes = np.array([axes])
    axes = axes.flatten()

    # Iterate through all SDF files
    for i, sdf_file in enumerate(sdf_files):
        if i >= len(axes):
            break

        # Load SDF
        volume = Volume(sdf_file)
        volume.load()
        sdf_data = volume.volume

        # Get middle slice
        z_mid = sdf_data.shape[0] // 2

        # Get filename and path information
        filename = os.path.basename(sdf_file)
        dataset_path = os.path.dirname(os.path.dirname(os.path.dirname(sdf_file)))
        dataset_name = os.path.basename(dataset_path)

        # Show SDF slice
        im = axes[i].imshow(sdf_data[z_mid], cmap="seismic", vmin=-30, vmax=30)
        axes[i].set_title(f"{dataset_name}\n{filename}\n(z={z_mid})")
        axes[i].axis("off")

        # Add colorbar
        plt.colorbar(im, ax=axes[i], fraction=0.046, pad=0.04)

    # Hide unused subplots
    for i in range(len(sdf_files), len(axes)):
        axes[i].axis("off")

    plt.tight_layout()
    plt.show()


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg: DictConfig):
    """Main function"""
    # Get visualization mode
    mode = cfg.get("mode", "single")  # single, all, or overview
    show_binary = cfg.get("show_binary", False)
    show_3d = cfg.get("show_3d", False)

    # Check if specific SDF file path is provided
    sdf_path = "F:/dev/CT/3d-seg/output/fib1-3-2-1_unet.zst"

    if sdf_path:
        # Use the specified SDF file path
        if os.path.exists(sdf_path):
            logger.info(f"Using specified SDF file: {sdf_path}")
            if mode == "single":
                visualize_sdf(sdf_path, show_binary=show_binary, show_3d=show_3d)
            else:
                logger.warning(
                    "When specifying a single SDF file, only 'single' mode is supported"
                )
                visualize_sdf(sdf_path, show_binary=show_binary, show_3d=show_3d)
        else:
            logger.error(f"Specified SDF file not found: {sdf_path}")
            return
    else:
        # Auto-find SDF files based on organelle type
        # Get dataset root directory
        datasets_root = cfg.datasets_root
        finetune_dir = os.path.join(datasets_root, "finetune")

        # Get organelle type, default is mito
        organelle_type = cfg.get("organelle_type", "mito")
        logger.info(f"Processing organelle type: {organelle_type}")

        # Find all SDF files for the specified organelle
        sdf_files = find_sdf_files(finetune_dir, organelle_type=organelle_type)

        if not sdf_files:
            logger.error(f"No SDF files found for {organelle_type}")
            return

        if mode == "all":
            # Visualize all SDF files one by one
            for sdf_file in sdf_files:
                visualize_sdf(sdf_file, show_binary=show_binary, show_3d=show_3d)
        elif mode == "overview":
            # Overview all SDF files
            visualize_all_sdfs(sdf_files, show_binary=show_binary)
        else:  # single mode
            # Visualize the first SDF file
            visualize_sdf(sdf_files[0], show_binary=show_binary, show_3d=show_3d)


if __name__ == "__main__":
    main()

import json
import csv
import re
from collections import defaultdict


def process_metrics_data(data):
    """
    处理原始指标数据，合并并平均相同数据集细胞器的指标。

    Args:
        data (dict): 包含文件路径和指标的字典。

    Returns:
        dict: 合并并平均后的指标字典，以及一个包含平均值的 'average' 键。
    """
    merged_data = defaultdict(lambda: defaultdict(float))
    file_counts = defaultdict(lambda: defaultdict(int))

    # 第一阶段：解析路径，累加指标，并统计文件数量
    for path, metrics in data.items():
        if path == "average":  # 跳过原始的全局平均值
            continue

        dataset_organelle = "未知_未知"
        # 使用正则表达式匹配 jrc_xxx-Y/sdf_avg/organelle 模式
        match = re.search(r"jrc_([a-zA-Z0-9-]+)/sdf_avg/([a-zA-Z0-9]+)/", path)
        if match:
            dataset_name = match.group(1).replace("jrc_", "")
            organelle_name = match.group(2)
            dataset_organelle = f"{dataset_name}_{organelle_name}"
        else:
            # 回退机制，如果正则表达式未匹配
            parts = path.split("/")
            # 尝试从倒数第四个元素获取数据集，从倒数第二个获取细胞器
            if len(parts) >= 10:
                dataset_full = parts[-4]
                organelle = parts[-2]
                if dataset_full.startswith("jrc_"):
                    dataset_full = dataset_full.replace("jrc_", "")
                dataset_organelle = f"{dataset_full}_{organelle}"
            elif len(parts) >= 2:
                dataset_organelle = f"fallback_{parts[-2]}"
            else:
                dataset_organelle = path  # 实在无法解析，就用完整路径

        # 累加指标
        merged_data[dataset_organelle]["dice"] += metrics.get("dice", 0.0)
        merged_data[dataset_organelle]["iou"] += metrics.get("iou", 0.0)
        merged_data[dataset_organelle]["precision"] += metrics.get("precision", 0.0)
        merged_data[dataset_organelle]["recall"] += metrics.get("recall", 0.0)
        # 统计文件数量
        file_counts[dataset_organelle]["count"] += 1

    # 第二阶段：计算平均值
    final_processed_data = {}
    total_dice = 0.0
    total_iou = 0.0
    total_precision = 0.0
    total_recall = 0.0
    total_entries = 0

    for key, metrics_sum in merged_data.items():
        count = file_counts[key]["count"]
        if count > 0:
            final_processed_data[key] = {
                "dice": metrics_sum["dice"] / count,
                "iou": metrics_sum["iou"] / count,
                "precision": metrics_sum["precision"] / count,
                "recall": metrics_sum["recall"] / count,
            }
            # 累加用于计算最终平均值的总和
            total_dice += final_processed_data[key]["dice"]
            total_iou += final_processed_data[key]["iou"]
            total_precision += final_processed_data[key]["precision"]
            total_recall += final_processed_data[key]["recall"]
            total_entries += 1

    # 计算所有合并后条目的总平均值
    if total_entries > 0:
        final_processed_data["average"] = {
            "dice": total_dice / total_entries,
            "iou": total_iou / total_entries,
            "precision": total_precision / total_entries,
            "recall": total_recall / total_entries,
        }
    else:
        final_processed_data["average"] = {
            "dice": 0.0,
            "iou": 0.0,
            "precision": 0.0,
            "recall": 0.0,
        }

    return final_processed_data


def print_metrics_table(processed_data):
    """
    将处理后的指标数据打印为表格。

    Args:
        processed_data (dict): 包含处理过的指标的字典。
    """
    table_rows = []
    # 按照数据集细胞器名称排序，确保输出顺序一致
    sorted_keys = sorted([k for k in processed_data.keys() if k != "average"])

    for key in sorted_keys:
        metrics = processed_data[key]
        dice = metrics.get("dice", 0.0)
        iou = metrics.get("iou", 0.0)
        precision = metrics.get("precision", 0.0)
        recall = metrics.get("recall", 0.0)
        table_rows.append(
            f"{key:<30} {dice:<10.4f} {iou:<10.4f} {precision:<10.4f} {recall:<10.4f}"
        )

    # 打印表头
    print(
        f"{'数据集_细胞器':<30} {'Dice':<10} {'IoU':<10} {'Precision':<10} {'Recall':<10}"
    )
    print("-" * 70)

    # 打印数据行
    for row in table_rows:
        print(row)

    # 打印总平均值
    average_metrics = processed_data.get("average", {})
    avg_dice = average_metrics.get("dice", 0.0)
    avg_iou = average_metrics.get("iou", 0.0)
    avg_precision = average_metrics.get("precision", 0.0)
    avg_recall = average_metrics.get("recall", 0.0)

    print("-" * 70)
    print(
        f"{'平均值':<30} {avg_dice:<10.4f} {avg_iou:<10.4f} {avg_precision:<10.4f} {avg_recall:<10.4f}"
    )


def save_metrics_to_csv(processed_data, filename="segmentation_metrics_merged.csv"):
    """
    将处理后的指标数据保存为CSV文件。

    Args:
        processed_data (dict): 包含处理过的指标的字典。
        filename (str): CSV文件名。
    """
    with open(filename, "w", newline="", encoding="utf-8") as csvfile:
        fieldnames = ["数据集_细胞器", "Dice", "IoU", "Precision", "Recall"]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()  # 写入表头

        sorted_keys = sorted([k for k in processed_data.keys() if k != "average"])
        for key in sorted_keys:
            metrics = processed_data[key]
            writer.writerow(
                {
                    "数据集_细胞器": key,
                    "Dice": metrics.get("dice", 0.0),
                    "IoU": metrics.get("iou", 0.0),
                    "Precision": metrics.get("precision", 0.0),
                    "Recall": metrics.get("recall", 0.0),
                }
            )

        # 写入平均值
        average_metrics = processed_data.get("average", {})
        writer.writerow(
            {
                "数据集_细胞器": "平均值",
                "Dice": average_metrics.get("dice", 0.0),
                "IoU": average_metrics.get("iou", 0.0),
                "Precision": average_metrics.get("precision", 0.0),
                "Recall": average_metrics.get("recall", 0.0),
            }
        )
    print(f"\n数据已保存到 {filename}")


# --- 使用示例 (占位空字典) ---
# 将您的实际数据粘贴到这里
metrics_data = {
    "/data2/hyk/dev/3d-seg/datasets/main/train/urocell/sdf_avg/mito/fib1-3-3-0_avg.zst": {
        "dice": 0.9509002603196798,
        "iou": 0.9063964314865205,
        "precision": 0.9669453586739013,
        "recall": 0.9353789619257854,
        "accuracy": 0.9063964314865205,
        "processing_time": 1.8010456562042236,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/urocell/sdf_avg/mito/fib1-4-3-0_avg.zst": {
        "dice": 0.9443391851462428,
        "iou": 0.894547919046388,
        "precision": 0.9489467571220503,
        "recall": 0.9397761407214398,
        "accuracy": 0.894547919046388,
        "processing_time": 1.5432393550872803,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/urocell/sdf_avg/fv/fib1-3-3-0_avg.zst": {
        "dice": 0.7322760315025362,
        "iou": 0.5776305013546812,
        "precision": 0.8102158299339164,
        "recall": 0.6680153946629253,
        "accuracy": 0.5776305013546812,
        "processing_time": 1.7684650421142578,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/urocell/sdf_avg/fv/fib1-4-3-0_avg.zst": {
        "dice": 0.7418532213856311,
        "iou": 0.5896396461807534,
        "precision": 0.8171626982269788,
        "recall": 0.6792534232559659,
        "accuracy": 0.5896396461807534,
        "processing_time": 1.7165913581848145,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/urocell/sdf_avg/lyso/fib1-3-3-0_avg.zst": {
        "dice": 0.0,
        "iou": 0.0,
        "precision": 0,
        "recall": 0.0,
        "accuracy": 0.0,
        "processing_time": 1.6169524192810059,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/urocell/sdf_avg/lyso/fib1-4-3-0_avg.zst": {
        "dice": 0.0,
        "iou": 0.0,
        "precision": 0,
        "recall": 0.0,
        "accuracy": 0.0,
        "processing_time": 1.612863540649414,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/urocell/sdf_avg/golgi/fib1-3-2-1_avg.zst": {
        "dice": 0.4431579123224789,
        "iou": 0.2846518062622374,
        "precision": 0.958160600166713,
        "recall": 0.28823447793954166,
        "accuracy": 0.2846518062622374,
        "processing_time": 1.540196418762207,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/urocell/sdf_avg/golgi/fib1-4-3-0_avg.zst": {
        "dice": 0.0,
        "iou": 0.0,
        "precision": 0,
        "recall": 0.0,
        "accuracy": 0.0,
        "processing_time": 1.8241987228393555,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_0_0_avg.zst": {
        "dice": 0.8223655833261544,
        "iou": 0.6983199299226277,
        "precision": 0.8702426675301456,
        "recall": 0.7794817806359233,
        "accuracy": 0.6983199299226277,
        "processing_time": 14.523932456970215,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_0_1_avg.zst": {
        "dice": 0.84474929172058,
        "iou": 0.7312259457332104,
        "precision": 0.9268122251442579,
        "recall": 0.7760365235475921,
        "accuracy": 0.7312259457332104,
        "processing_time": 14.28540825843811,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_0_2_avg.zst": {
        "dice": 0.8144597379021155,
        "iou": 0.6869945829261674,
        "precision": 0.9037445269313047,
        "recall": 0.741230413548259,
        "accuracy": 0.6869945829261674,
        "processing_time": 14.835727214813232,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_0_3_avg.zst": {
        "dice": 0.8287074026073905,
        "iou": 0.7075152736832445,
        "precision": 0.9049215660279748,
        "recall": 0.7643338001439822,
        "accuracy": 0.7075152736832445,
        "processing_time": 15.330163478851318,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_1_0_avg.zst": {
        "dice": 0.8404183522191683,
        "iou": 0.7247599630673119,
        "precision": 0.9181291120713733,
        "recall": 0.7748359723747694,
        "accuracy": 0.7247599630673119,
        "processing_time": 14.866174936294556,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_1_1_avg.zst": {
        "dice": 0.8431506197018244,
        "iou": 0.7288335318851137,
        "precision": 0.8891509594070449,
        "recall": 0.8016758177464941,
        "accuracy": 0.7288335318851137,
        "processing_time": 14.872024536132812,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_1_2_avg.zst": {
        "dice": 0.8245425980225414,
        "iou": 0.701465316084975,
        "precision": 0.9174673592852542,
        "recall": 0.748710233028812,
        "accuracy": 0.701465316084975,
        "processing_time": 15.001660823822021,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_1_3_avg.zst": {
        "dice": 0.7841363126331367,
        "iou": 0.644921236467965,
        "precision": 0.8960626900891646,
        "recall": 0.6970663832751782,
        "accuracy": 0.644921236467965,
        "processing_time": 14.994510173797607,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_2_0_avg.zst": {
        "dice": 0.7728554899550258,
        "iou": 0.6297999001981446,
        "precision": 0.8715516600814776,
        "recall": 0.6942385896694393,
        "accuracy": 0.6297999001981446,
        "processing_time": 14.44669771194458,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_2_1_avg.zst": {
        "dice": 0.8197730330545815,
        "iou": 0.6945893086787036,
        "precision": 0.8906597328673919,
        "recall": 0.7593380889543788,
        "accuracy": 0.6945893086787036,
        "processing_time": 14.333361625671387,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_2_2_avg.zst": {
        "dice": 0.7702504814401637,
        "iou": 0.6263474551648589,
        "precision": 0.8553690378878342,
        "recall": 0.7005391854292549,
        "accuracy": 0.6263474551648589,
        "processing_time": 14.39004373550415,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_2_3_avg.zst": {
        "dice": 0.8349455484944013,
        "iou": 0.7166579617076284,
        "precision": 0.9424669575355124,
        "recall": 0.7494450565799797,
        "accuracy": 0.7166579617076284,
        "processing_time": 14.30687141418457,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_3_0_avg.zst": {
        "dice": 0.8615650517531606,
        "iou": 0.7567977889996689,
        "precision": 0.9434483645057817,
        "recall": 0.7927601936236746,
        "accuracy": 0.7567977889996689,
        "processing_time": 14.257920026779175,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_3_1_avg.zst": {
        "dice": 0.8228881697717044,
        "iou": 0.6990739100906912,
        "precision": 0.9170461443637886,
        "recall": 0.7462651794881058,
        "accuracy": 0.6990739100906912,
        "processing_time": 13.951836824417114,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_3_2_avg.zst": {
        "dice": 0.8235322264153033,
        "iou": 0.7000040671798438,
        "precision": 0.9193316704921516,
        "recall": 0.745814256452919,
        "accuracy": 0.7000040671798438,
        "processing_time": 14.497592687606812,
    },
    "/data2/hyk/dev/3d-seg/datasets/main/train/mitoem/sdf_avg/mito/em_0_3_3_avg.zst": {
        "dice": 0.7981613187400388,
        "iou": 0.6641168496118608,
        "precision": 0.8847024561925299,
        "recall": 0.7270424059911647,
        "accuracy": 0.6641168496118608,
        "processing_time": 14.495437860488892,
    },
    "average": {
        "dice": 0.7049594928514108,
        "iou": 0.5985120552388581,
        "precision": 0.7896890989390228,
        "recall": 0.646228011624816,
        "accuracy": 0.5985120552388581,
        "processing_time": 10.283871511618296,
    },
}


# 处理并打印表格
processed_results = process_metrics_data(metrics_data)
print_metrics_table(processed_results)

# 保存到CSV
save_metrics_to_csv(processed_results)
